{"files": [{"path": "xlators/mgmt/glusterd/src/glusterd-syncop.c", "bookmarks": [{"line": 1810, "column": 0, "label": ""}, {"line": 1920, "column": 7, "label": ""}]}, {"path": "xlators/features/locks/src/locks.h", "bookmarks": [{"line": 9, "column": 10, "label": ""}]}, {"path": "xlators/cluster/dht/src/dht-diskusage.c", "bookmarks": [{"line": 207, "column": 26, "label": ""}, {"line": 245, "column": 7, "label": ""}, {"line": 257, "column": 14, "label": ""}]}, {"path": "glusterfsd/src/glusterfsd.c", "bookmarks": [{"line": 2327, "column": 11, "label": ""}, {"line": 2583, "column": 20, "label": ""}, {"line": 2693, "column": 8, "label": ""}, {"line": 2702, "column": 11, "label": ""}]}, {"path": "libglusterfs/src/inode.c", "bookmarks": [{"line": 158, "column": 12, "label": ""}]}, {"path": "rpc/rpc-lib/src/rpc-clnt.c", "bookmarks": [{"line": 172, "column": 12, "label": ""}, {"line": 953, "column": 16, "label": ""}, {"line": 1970, "column": 14, "label": ""}]}, {"path": "xlators/protocol/client/src/client-messages.h", "bookmarks": [{"line": 124, "column": 3, "label": ""}]}, {"path": "xlators/protocol/client/src/client-handshake.c", "bookmarks": [{"line": 1215, "column": 21, "label": ""}]}, {"path": "xlators/protocol/client/src/client.c", "bookmarks": [{"line": 2227, "column": 29, "label": ""}, {"line": 2287, "column": 20, "label": ""}, {"line": 2586, "column": 21, "label": ""}]}, {"path": "xlators/protocol/server/src/server-messages.h", "bookmarks": [{"line": 75, "column": 4, "label": ""}]}, {"path": "xlators/protocol/server/src/server.c", "bookmarks": [{"line": 498, "column": 14, "label": ""}, {"line": 1035, "column": 1, "label": ""}]}, {"path": "api/src/glfs.h", "bookmarks": [{"line": 150, "column": 1, "label": ""}, {"line": 888, "column": 5, "label": ""}, {"line": 926, "column": 5, "label": ""}, {"line": 938, "column": 39, "label": ""}, {"line": 950, "column": 6, "label": ""}]}, {"path": "api/src/glfs-fops.c", "bookmarks": [{"line": 4217, "column": 13, "label": ""}]}, {"path": "../../../../usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h", "bookmarks": [{"line": 10, "column": 5, "label": ""}]}]}